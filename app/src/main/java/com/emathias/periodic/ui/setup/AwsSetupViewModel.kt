package com.emathias.periodic.ui.setup

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.emathias.periodic.config.AwsCredentialManager
import com.emathias.periodic.service.BedrockAiService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class AwsSetupState(
    val accessKeyId: String = "",
    val secretAccessKey: String = "",
    val region: String = "us-east-1",
    val accessKeyIdError: String? = null,
    val secretAccessKeyError: String? = null,
    val errorMessage: String? = null,
    val isLoading: Boolean = false,
    val isTestingConnection: Boolean = false,
    val connectionTestSuccess: Boolean = false
) {
    val isValid: Boolean
        get() = accessKeyId.isNotBlank() && 
                secretAccessKey.isNotBlank() && 
                region.isNotBlank() &&
                accessKeyIdError == null && 
                secretAccessKeyError == null
}

@HiltViewModel
class AwsSetupViewModel @Inject constructor(
    private val credentialManager: AwsCredentialManager,
    private val bedrockService: BedrockAiService
) : ViewModel() {

    private val _state = MutableStateFlow(AwsSetupState())
    val state: StateFlow<AwsSetupState> = _state.asStateFlow()

    init {
        // Load existing credentials if available
        if (credentialManager.areCredentialsConfigured()) {
            _state.value = _state.value.copy(
                region = credentialManager.getRegion()
            )
        }
    }

    fun updateAccessKeyId(accessKeyId: String) {
        _state.value = _state.value.copy(
            accessKeyId = accessKeyId,
            accessKeyIdError = validateAccessKeyId(accessKeyId),
            errorMessage = null
        )
    }

    fun updateSecretAccessKey(secretAccessKey: String) {
        _state.value = _state.value.copy(
            secretAccessKey = secretAccessKey,
            secretAccessKeyError = validateSecretAccessKey(secretAccessKey),
            errorMessage = null
        )
    }

    fun updateRegion(region: String) {
        _state.value = _state.value.copy(
            region = region,
            errorMessage = null
        )
    }

    fun saveCredentials() {
        if (!_state.value.isValid) return

        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true, errorMessage = null)

            try {
                // Save credentials
                credentialManager.setCredentials(
                    _state.value.accessKeyId,
                    _state.value.secretAccessKey
                )
                credentialManager.setRegion(_state.value.region)

                // Test connection
                _state.value = _state.value.copy(
                    isLoading = false,
                    isTestingConnection = true
                )

                val testResult = bedrockService.testConnection()
                
                if (testResult.isSuccess) {
                    _state.value = _state.value.copy(
                        isTestingConnection = false,
                        connectionTestSuccess = true
                    )
                } else {
                    _state.value = _state.value.copy(
                        isTestingConnection = false,
                        errorMessage = "Connection test failed: ${testResult.exceptionOrNull()?.message}"
                    )
                    // Clear credentials on failure
                    credentialManager.clearCredentials()
                }
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    isLoading = false,
                    isTestingConnection = false,
                    errorMessage = "Failed to save credentials: ${e.message}"
                )
            }
        }
    }

    private fun validateAccessKeyId(accessKeyId: String): String? {
        return when {
            accessKeyId.isBlank() -> "Access Key ID is required"
            accessKeyId.length < 16 -> "Access Key ID seems too short"
            !accessKeyId.matches(Regex("^[A-Z0-9]+$")) -> "Access Key ID should contain only uppercase letters and numbers"
            else -> null
        }
    }

    private fun validateSecretAccessKey(secretAccessKey: String): String? {
        return when {
            secretAccessKey.isBlank() -> "Secret Access Key is required"
            secretAccessKey.length < 20 -> "Secret Access Key seems too short"
            else -> null
        }
    }
}
