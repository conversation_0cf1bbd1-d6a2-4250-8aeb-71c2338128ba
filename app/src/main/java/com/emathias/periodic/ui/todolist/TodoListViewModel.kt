package com.emathias.periodic.ui.todolist

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.emathias.periodic.db.dao.TodoItemDao
import com.emathias.periodic.service.AiEnhancedTodoService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class TodoListViewModel(
    private val dao: TodoItemDao,
    private val aiService: AiEnhancedTodoService,
) : ViewModel() {

    private val _state = MutableStateFlow(TodoListState())
    private val _todoItems =
        dao.getAll().stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())

    val state = combine(_state, _todoItems) { state, todoItems ->
        state.copy(todoItems = todoItems)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), TodoListState())

    fun onEvent(event: TodoListEvent) {
        when (event) {
            is TodoListEvent.Check -> {
                viewModelScope.launch {
                    dao.update(event.todoItem.copy(checked = true))
                }
            }

            is TodoListEvent.Uncheck -> {
                viewModelScope.launch {
                    dao.update(event.todoItem.copy(checked = false))
                }
            }

            TodoListEvent.ShowAddDialog -> _state.update { it.copy(showingAddDialog = true) }

            TodoListEvent.HideAddDialog -> _state.update { it.copy(showingAddDialog = false) }

            is TodoListEvent.ShowConfirmDialog -> _state.update {
                it.copy(
                    showingConfirmDialog = true,
                    pendingScheduledItem = event.scheduledItem,
                )
            }

            TodoListEvent.HideConfirmDialog -> _state.update {
                it.copy(
                    showingConfirmDialog = false,
                    pendingScheduledItem = null,
                )
            }

            is TodoListEvent.AddItem -> viewModelScope.launch {
                dao.insert(event.todoItem)
            }

            is TodoListEvent.GenerateItem -> viewModelScope.launch {
                val scheduledItem = aiService.generateScheduledItem(event.prompt).getOrThrow()
                onEvent(TodoListEvent.HideAddDialog)
                onEvent(TodoListEvent.ShowConfirmDialog(scheduledItem))
            }
        }
    }
}